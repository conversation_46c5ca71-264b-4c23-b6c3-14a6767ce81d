#!/usr/bin/env python3
"""
Test script to verify Do Not Disturb functionality.
This script tests the DND toggle in the control center and notification filtering.
"""

import sys
import os
import time
import subprocess
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from utils.roam import modus_service


def send_test_notification(title="Test Notification", body="This is a test notification"):
    """Send a test notification using notify-send."""
    try:
        subprocess.run([
            "notify-send", 
            "-a", "DND Test",
            "-i", "dialog-information",
            title, 
            body
        ], check=True)
        print(f"✓ Sent notification: {title}")
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to send notification: {e}")
    except FileNotFoundError:
        print("✗ notify-send not found. Please install libnotify.")


def test_dnd_functionality():
    """Test the DND functionality."""
    print("=== Testing Do Not Disturb Functionality ===\n")
    
    # Test 1: Check initial DND state
    print("1. Checking initial DND state...")
    initial_state = modus_service.dont_disturb
    print(f"   Initial DND state: {initial_state}")
    
    # Test 2: Send notification with DND off
    print("\n2. Testing notification with DND OFF...")
    modus_service.dont_disturb = False
    time.sleep(0.5)  # Give time for state to propagate
    send_test_notification("DND OFF Test", "You should see this notification")
    
    # Test 3: Enable DND
    print("\n3. Enabling DND...")
    modus_service.dont_disturb = True
    print(f"   DND state: {modus_service.dont_disturb}")
    
    # Test 4: Send notification with DND on
    print("\n4. Testing notification with DND ON...")
    time.sleep(0.5)  # Give time for state to propagate
    send_test_notification("DND ON Test", "You should NOT see this notification")
    
    # Test 5: Disable DND
    print("\n5. Disabling DND...")
    modus_service.dont_disturb = False
    print(f"   DND state: {modus_service.dont_disturb}")
    
    # Test 6: Send final notification
    print("\n6. Testing notification with DND OFF again...")
    time.sleep(0.5)  # Give time for state to propagate
    send_test_notification("DND OFF Final Test", "You should see this notification again")
    
    # Restore initial state
    print(f"\n7. Restoring initial DND state: {initial_state}")
    modus_service.dont_disturb = initial_state
    
    print("\n=== DND Test Complete ===")
    print("\nExpected behavior:")
    print("- You should have seen the first and last notifications")
    print("- The middle notification (DND ON Test) should have been blocked")
    print("- Check your notification center to see if blocked notifications were cached")


if __name__ == "__main__":
    try:
        test_dnd_functionality()
    except KeyboardInterrupt:
        print("\n\nTest interrupted by user.")
    except Exception as e:
        print(f"\n✗ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
